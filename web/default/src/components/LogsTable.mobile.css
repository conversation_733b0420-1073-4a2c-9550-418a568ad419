/* ==============================|| MODERN MOBILE LOGS TABLE ||============================== */

/* Mobile-first responsive design for LogsTable component */
@media screen and (max-width: 768px) {

  /* ==============================|| LAYOUT RESET ||============================== */

  /* Override global body padding that causes top margin issue */
  body {
    padding-top: 0 !important;
  }

  /* Reset container margins and padding */
  .ui.container {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
  }

  .main-content {
    padding: 0 !important;
    margin: 0 !important;
  }

  /* ==============================|| HEADER FIXES ||============================== */

  /* Fix header positioning and overlay issues */
  .ui.header {
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
    background-color: var(--bg-primary, #ffffff) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    margin: 0 !important;
    padding: 12px 16px !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  .ui.header h3 {
    font-size: 18px !important;
    font-weight: 600 !important;
    margin: 0 !important;
    line-height: 1.3 !important;
    color: var(--text-primary, #212529) !important;
  }

  /* ==============================|| FORM OPTIMIZATION ||============================== */

  /* Modern form styling */
  .ui.form {
    padding: 16px !important;
    margin: 0 !important;
    background-color: var(--bg-secondary, #f8f9fa) !important;
  }

  .ui.form .field {
    margin-bottom: 16px !important;
  }

  .ui.form .field:last-child {
    margin-bottom: 0 !important;
  }

  /* Full-width form controls */
  .ui.form .ui.input,
  .ui.form .ui.dropdown,
  .ui.form .ui.button {
    width: 100% !important;
    min-height: 44px !important; /* Touch-friendly size */
  }

  .ui.form .ui.input input {
    padding: 12px 16px !important;
    font-size: 16px !important; /* Prevent zoom on iOS */
    border-radius: 8px !important;
    border: 1px solid var(--input-border, #ced4da) !important;
    background-color: var(--input-bg, #ffffff) !important;
  }

  /* ==============================|| TABLE CARD LAYOUT ||============================== */

  /* Hide traditional table structure */
  .ui.table {
    border: none !important;
    box-shadow: none !important;
    margin: 0 !important;
    background: transparent !important;
  }

  .ui.table thead {
    display: none !important;
  }

  /* Card-based row layout */
  .ui.table tbody tr {
    display: block !important;
    background-color: var(--card-bg, #ffffff) !important;
    border: none !important;
    border-radius: 12px !important;
    margin: 16px !important;
    padding: 16px !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
    transition: transform 0.2s ease, box-shadow 0.2s ease !important;
  }

  .ui.table tbody tr:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12) !important;
  }

  /* Cell styling with labels */
  .ui.table tbody tr td {
    display: flex !important;
    align-items: flex-start !important;
    padding: 8px 0 !important;
    border: none !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    line-height: 1.5 !important;
    min-height: auto !important;
  }

  .ui.table tbody tr td:before {
    content: attr(data-label) !important;
    font-weight: 600 !important;
    color: var(--text-secondary, #6c757d) !important;
    font-size: 14px !important;
    min-width: 80px !important;
    margin-right: 12px !important;
    flex-shrink: 0 !important;
  }

  .ui.table tbody tr td > * {
    flex: 1 !important;
    margin: 0 !important;
  }

  /* ==============================|| COMPLETE BORDER REMOVAL ||============================== */

  /* Remove all table borders */
  .ui.table,
  .ui.table thead,
  .ui.table tbody,
  .ui.table tfoot,
  .ui.table tr,
  .ui.table td,
  .ui.table th {
    border: none !important;
    border-top: none !important;
    border-bottom: none !important;
    border-left: none !important;
    border-right: none !important;
    border-collapse: separate !important;
  }

  /* ==============================|| FOOTER AND PAGINATION ||============================== */

  /* Table footer */
  .ui.table tfoot tr {
    display: block !important;
    background-color: var(--bg-primary, #ffffff) !important;
    padding: 16px !important;
    margin: 0 !important;
    border-radius: 0 !important;
    box-shadow: none !important;
  }

  .ui.table tfoot tr th {
    display: block !important;
    border: none !important;
    padding: 0 !important;
  }

  /* Modern pagination */
  .ui.pagination.menu {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 8px !important;
    margin: 16px 0 !important;
    padding: 0 16px !important;
  }

  .ui.pagination.menu .item {
    min-width: 44px !important;
    height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border: 1px solid var(--border-color, #dee2e6) !important;
    background-color: var(--bg-primary, #ffffff) !important;
    color: var(--text-primary, #212529) !important;
    transition: all 0.2s ease !important;
  }

  .ui.pagination.menu .item:hover,
  .ui.pagination.menu .item.active {
    background-color: var(--button-primary, #007bff) !important;
    color: white !important;
    border-color: var(--button-primary, #007bff) !important;
  }

  /* ==============================|| COMPONENT STYLING ||============================== */

  /* Modern labels */
  .ui.label {
    border-radius: 6px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    padding: 4px 8px !important;
    margin: 2px !important;
    border: 1px solid transparent !important;
  }

  /* Modern buttons */
  .ui.button {
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    padding: 12px 20px !important;
    margin: 8px 0 !important;
    min-height: 44px !important;
    border: 1px solid var(--border-color, #dee2e6) !important;
    transition: all 0.2s ease !important;
  }

  .ui.button.primary {
    background-color: var(--button-primary, #007bff) !important;
    color: white !important;
    border-color: var(--button-primary, #007bff) !important;
  }

  /* Modern dropdowns */
  .ui.dropdown {
    border-radius: 8px !important;
    border: 1px solid var(--input-border, #ced4da) !important;
    background-color: var(--input-bg, #ffffff) !important;
    min-height: 44px !important;
    margin: 8px 0 !important;
  }

  /* ==============================|| DARK THEME SUPPORT ||============================== */

  [data-theme="dark"] .ui.table tbody tr {
    background-color: var(--card-bg, #2a2a2a) !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3) !important;
  }

  [data-theme="dark"] .ui.header {
    background-color: var(--bg-primary, #1e1e1e) !important;
    color: var(--text-primary, #e4e4e4) !important;
  }

  [data-theme="dark"] .ui.form {
    background-color: var(--bg-secondary, #2a2a2a) !important;
  }
}
